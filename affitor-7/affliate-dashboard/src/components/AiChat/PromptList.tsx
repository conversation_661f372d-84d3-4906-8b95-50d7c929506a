"use client";

import React from 'react';
import { Star, Edit, Trash2, Play, Clock, Tag, AlertCircle } from 'lucide-react';
import { UserPrompt } from '@/features/aiscript/aiscript.slice';

interface PromptListProps {
  prompts: UserPrompt[];
  isLoading: boolean;
  error: string | null;
  onEdit: (prompt: UserPrompt) => void;
  onDelete: (promptId: string) => void;
  onToggleFavorite: (promptId: string) => void;
  onUse: (prompt: UserPrompt) => void;
}

export default function PromptList({
  prompts,
  isLoading,
  error,
  onEdit,
  onDelete,
  onToggleFavorite,
  onUse,
}: PromptListProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatUsageCount = (count: number) => {
    if (count === 0) return 'Never used';
    if (count === 1) return 'Used once';
    return `Used ${count} times`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3 text-gray-500 dark:text-gray-400">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span>Loading prompts...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3 text-red-500 dark:text-red-400">
          <AlertCircle size={24} />
          <span>Error loading prompts: {error}</span>
        </div>
      </div>
    );
  }

  if (prompts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <Tag size={24} />
          </div>
          <h3 className="text-lg font-medium mb-2">No prompts found</h3>
          <p className="text-sm">Create your first prompt to get started</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto p-4">
      <div className="space-y-3">
        {prompts.map((prompt) => (
          <div
            key={prompt.id}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 truncate">
                  {prompt.title}
                </h3>
                {prompt.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                    {prompt.description}
                  </p>
                )}
              </div>
              <div className="flex items-center gap-1 ml-3">
                <button
                  onClick={() => onToggleFavorite(prompt.id)}
                  className={`p-1.5 rounded-md transition-colors ${
                    prompt.isFavorite
                      ? 'text-yellow-500 hover:text-yellow-600'
                      : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                  }`}
                  title={prompt.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                >
                  <Star size={16} fill={prompt.isFavorite ? 'currentColor' : 'none'} />
                </button>
                <button
                  onClick={() => onEdit(prompt)}
                  className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors"
                  title="Edit prompt"
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => onDelete(prompt.id)}
                  className="p-1.5 text-gray-400 hover:text-red-500 rounded-md transition-colors"
                  title="Delete prompt"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            {/* Content Preview */}
            <div className="mb-3">
              <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                {prompt.content}
              </p>
            </div>

            {/* Tags */}
            {prompt.tags && prompt.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {prompt.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <Clock size={12} />
                  {formatUsageCount(prompt.usageCount)}
                </span>
                <span>Updated {formatDate(prompt.updatedAt)}</span>
              </div>
              <button
                onClick={() => onUse(prompt)}
                className="flex items-center gap-1 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-xs transition-colors"
              >
                <Play size={12} />
                Use Prompt
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
