"use client";

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { X, Search, Plus, Star, Clock, Tag, Filter } from 'lucide-react';
import {
  actions,
  selectPromptManager,
  selectUserPrompts,
  selectUserPromptsLoading,
  selectUserPromptsError,
  selectAvailableTags,
  selectRecentPrompts,
  UserPrompt
} from '@/features/aiscript/aiscript.slice';
import PromptList from './PromptList';
import PromptEditor from './PromptEditor';

export default function PromptManager() {
  const dispatch = useDispatch();
  const promptManager = useSelector(selectPromptManager);
  const userPrompts = useSelector(selectUserPrompts);
  const isLoading = useSelector(selectUserPromptsLoading);
  const error = useSelector(selectUserPromptsError);
  const availableTags = useSelector(selectAvailableTags);
  const recentPrompts = useSelector(selectRecentPrompts);

  const [showEditor, setShowEditor] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<UserPrompt | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Fetch prompts when manager opens
  useEffect(() => {
    if (promptManager.isOpen) {
      dispatch(actions.fetchUserPrompts());
    }
  }, [promptManager.isOpen, dispatch]);

  // Filter prompts based on current view and search
  const filteredPrompts = React.useMemo(() => {
    let prompts = userPrompts;

    // Apply view filter
    if (promptManager.view === 'favorites') {
      prompts = prompts.filter(p => p.isFavorite);
    } else if (promptManager.view === 'recent') {
      prompts = recentPrompts;
    }

    // Apply search filter
    if (promptManager.searchQuery) {
      const query = promptManager.searchQuery.toLowerCase();
      prompts = prompts.filter(p => 
        p.title.toLowerCase().includes(query) ||
        p.content.toLowerCase().includes(query) ||
        p.description?.toLowerCase().includes(query) ||
        p.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply tag filter
    if (promptManager.selectedTags.length > 0) {
      prompts = prompts.filter(p => 
        p.tags?.some(tag => promptManager.selectedTags.includes(tag))
      );
    }

    return prompts;
  }, [userPrompts, recentPrompts, promptManager.view, promptManager.searchQuery, promptManager.selectedTags]);

  const handleClose = () => {
    dispatch(actions.closePromptManager());
    setShowEditor(false);
    setEditingPrompt(null);
  };

  const handleCreateNew = () => {
    setEditingPrompt(null);
    setShowEditor(true);
  };

  const handleEditPrompt = (prompt: UserPrompt) => {
    setEditingPrompt(prompt);
    setShowEditor(true);
  };

  const handleDeletePrompt = (promptId: string) => {
    if (confirm('Are you sure you want to delete this prompt?')) {
      // TODO: Implement delete action
      console.log('Delete prompt:', promptId);
    }
  };

  const handleToggleFavorite = (promptId: string) => {
    // TODO: Implement toggle favorite action
    console.log('Toggle favorite:', promptId);
  };

  const handleUsePrompt = (prompt: UserPrompt) => {
    // TODO: Implement use prompt action
    console.log('Use prompt:', prompt);
    handleClose();
  };

  const handleViewChange = (view: 'all' | 'favorites' | 'recent') => {
    dispatch(actions.setPromptManagerView(view));
  };

  const handleSearchChange = (query: string) => {
    dispatch(actions.setPromptManagerSearch(query));
  };

  const handleTagToggle = (tag: string) => {
    const currentTags = promptManager.selectedTags;
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    dispatch(actions.setPromptManagerTags(newTags));
  };

  if (!promptManager.isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Prompt Library
          </h2>
          <div className="flex items-center gap-2">
            <button
              onClick={handleCreateNew}
              className="flex items-center gap-2 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm transition-colors"
            >
              <Plus size={16} />
              New Prompt
            </button>
            <button
              onClick={handleClose}
              className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
            >
              <X size={20} className="text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 mb-3">
            <div className="relative flex-1">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search prompts..."
                value={promptManager.searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 px-3 py-2 border rounded-md transition-colors ${
                showFilters || promptManager.selectedTags.length > 0
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300'
                  : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
              }`}
            >
              <Filter size={16} />
              Filters
              {promptManager.selectedTags.length > 0 && (
                <span className="bg-blue-600 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
                  {promptManager.selectedTags.length}
                </span>
              )}
            </button>
          </div>

          {/* View Tabs */}
          <div className="flex items-center gap-1">
            {[
              { key: 'all', label: 'All Prompts', icon: null },
              { key: 'favorites', label: 'Favorites', icon: Star },
              { key: 'recent', label: 'Recent', icon: Clock },
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => handleViewChange(key as any)}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm transition-colors ${
                  promptManager.view === key
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                {Icon && <Icon size={14} />}
                {label}
              </button>
            ))}
          </div>

          {/* Tag Filters */}
          {showFilters && availableTags.length > 0 && (
            <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
              <div className="flex items-center gap-2 mb-2">
                <Tag size={14} className="text-gray-500 dark:text-gray-400" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Filter by tags:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {availableTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-2 py-1 text-xs rounded-full transition-colors ${
                      promptManager.selectedTags.includes(tag)
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {showEditor ? (
            <PromptEditor
              prompt={editingPrompt}
              onSave={() => {
                setShowEditor(false);
                setEditingPrompt(null);
                dispatch(actions.fetchUserPrompts());
              }}
              onCancel={() => {
                setShowEditor(false);
                setEditingPrompt(null);
              }}
            />
          ) : (
            <PromptList
              prompts={filteredPrompts}
              isLoading={isLoading}
              error={error}
              onEdit={handleEditPrompt}
              onDelete={handleDeletePrompt}
              onToggleFavorite={handleToggleFavorite}
              onUse={handleUsePrompt}
            />
          )}
        </div>
      </div>
    </div>
  );
}
