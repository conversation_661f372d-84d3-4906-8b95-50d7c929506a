import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<any | AppError>
) {
  const { method } = req;

  if (method !== "PUT") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const token = authHeader.split(" ")[1];
    
    // Get the profile data from the request body
    const profileData = req.body;
    
    // Call the Strapi API to update the user profile
    const response = await StrapiClient.client.put("/api/users/profile", profileData, {
      headers: {
        Authorization: `Bear<PERSON> ${token}`,
      },
    });
    
    return res.status(200).json(response);
  } catch (error: any) {
    console.error("Error in /api/users/profile:", error);
    sendApiError(res, error, "Error updating user profile");
  }
}
