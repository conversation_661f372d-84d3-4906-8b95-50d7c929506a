import { NextApiRequest, NextApiResponse } from 'next';
import { createApiContext } from '@/utils/api-middleware';
import { sendApiError } from '@/utils/api-error-handler';
import { StrapiClient } from '@/utils/request';

interface UserPrompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  tags?: string[];
  isFavorite: boolean;
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserPromptsResponse {
  data: UserPrompt[];
  meta?: {
    pagination: {
      start: number;
      limit: number;
    };
  };
}

interface CreatePromptRequest {
  title: string;
  content: string;
  description?: string;
  tags?: string[];
  isFavorite?: boolean;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<UserPromptsResponse | { data: UserPrompt; message: string } | { message: string } | any>
) {
  try {
    const { token } = createApiContext(req, { requireAuth: true });

    if (req.method === 'GET') {
      // Get user prompts with optional search and filtering
      const { search, tags, isFavorite, limit, start, sortBy, sortOrder } = req.query;
      
      const queryParams = new URLSearchParams();
      if (search) queryParams.append('search', search as string);
      if (tags) queryParams.append('tags', tags as string);
      if (isFavorite !== undefined) queryParams.append('isFavorite', isFavorite as string);
      if (limit) queryParams.append('limit', limit as string);
      if (start) queryParams.append('start', start as string);
      if (sortBy) queryParams.append('sortBy', sortBy as string);
      if (sortOrder) queryParams.append('sortOrder', sortOrder as string);

      const response = await StrapiClient.client.get(`/api/user-prompts?${queryParams.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return res.status(200).json(response.data);
    }

    if (req.method === 'POST') {
      // Create new user prompt
      const promptData: CreatePromptRequest = req.body;

      // Validate required fields
      if (!promptData.title || !promptData.content) {
        return res.status(400).json({ error: 'Title and content are required' });
      }

      const response = await StrapiClient.client.post('/api/user-prompts', promptData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return res.status(201).json(response.data);
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error: any) {
    console.error('Error in user prompts API route:', error);
    sendApiError(res, error, 'Error processing user prompts request');
  }
}
