"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useSelector, useDispatch } from "react-redux";
import {
  selectAdminIsAuthenticated,
  selectAdminData,
  selectAdminLoading,
} from "@/features/selectors";
import { adminActions } from "@/features/rootActions";
import { AppDispatch } from "@/store";
import AdminDashboard from "./Dashboard";
import AdminPartner from "./Partner";
import AdminCustomer from "./Customer";
import AdminPayout from "./Payout";
import AdminSettings from "./Settings";

// Define the valid hash sections
type Section =
  | "dashboard"
  | "partners"
  | "customers"
  | "payout"
  | "settings"
  | "";

const validSections: Section[] = [
  "dashboard",
  "partners",
  "customers",
  "payout",
  "settings",
];

const AdminContainer: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const isAuthenticated = useSelector(selectAdminIsAuthenticated);
  const adminData = useSelector(selectAdminData);
  const isLoadingAdmin = useSelector(selectAdminLoading);

  // Track the current active section based on URL hash
  const [activeSection, setActiveSection] = useState<Section>(() => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash.replace("#", "");
      return validSections.includes(hash as Section)
        ? (hash as Section)
        : "dashboard";
    }
    return "dashboard";
  });
  const [mounted, setMounted] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Fix hydration mismatch by ensuring client-side rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  // Check authentication status on mount
  useEffect(() => {
    if (!mounted) return;

    // Check if admin token exists and validate it
    const token =
      typeof window !== "undefined"
        ? localStorage.getItem("admin_token")
        : null;
    if (token) {
      dispatch(adminActions.checkAuthStatus());
    } else {
      // No token found, user needs to login
      console.log("No admin token found");
    }
  }, [mounted, dispatch]);

  // Handle hash-based navigation and persistence
  useEffect(() => {
    if (!mounted) return;

    // Set initial section based on hash from URL on mount
    const hash = window.location.hash.replace("#", "");
    if (validSections.includes(hash as Section)) {
      setActiveSection(hash as Section);
    } else {
      setActiveSection("dashboard");
      if (typeof window !== "undefined") {
        window.history.replaceState(null, "", "#dashboard");
      }
    }

    // Add event listener for hash changes
    const handleHashChange = () => {
      const newHash = window.location.hash.replace("#", "");
      if (validSections.includes(newHash as Section)) {
        setActiveSection(newHash as Section);
      } else {
        setActiveSection("dashboard");
        window.history.replaceState(null, "", "#dashboard");
      }
    };

    window.addEventListener("hashchange", handleHashChange);

    // Cleanup event listener
    return () => {
      window.removeEventListener("hashchange", handleHashChange);
    };
  }, [mounted]);

  // Don't render anything until mounted to prevent hydration mismatches
  if (!mounted) {
    return null;
  }

  // Handler for logout
  const handleLogout = () => {
    dispatch(adminActions.logout());
    // Redirect to login page after logout
    router.push("/admin/authentication");
  };

  // Navigation items for sidebar
  const navItems = [
    { name: "Dashboard", hash: "dashboard", icon: "📊" },
    { name: "Partners", hash: "partners", icon: "🤝" },
    { name: "Customers", hash: "customers", icon: "👥" },
    { name: "Payout", hash: "payout", icon: "💰" },
    { name: "Settings", hash: "settings", icon: "⚙️" },
  ];

  // Handle navigation item click (close mobile menu and update hash)
  const handleNavClick = (hash: string) => {
    setIsMobileMenuOpen(false);
    setActiveSection(hash as Section);
    window.location.hash = hash;
  };

  // Render section content based on activeSection
  const renderSectionContent = () => {
    // Show loading while fetching admin data
    if (isLoadingAdmin && !adminData) {
      return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    // Render specific section content based on activeSection
    switch (activeSection) {
      case "dashboard":
        return <AdminDashboard />;
      case "partners":
        return <AdminPartner />;
      case "customers":
        return <AdminCustomer />;
      case "payout":
        return <AdminPayout />;
      case "settings":
        return <AdminSettings />;
      default:
        // Default to dashboard
        if (mounted) {
          setTimeout(() => {
            window.location.hash = "dashboard";
          }, 0);
        }
        return <AdminDashboard />;
    }
  };

  if (!isAuthenticated) {
    return null; // This should be handled by the parent page
  }

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
        fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"}
        border-r bg-white dark:bg-gray-800 shadow-sm border-gray-200 dark:border-gray-700
      `}
      >
        {/* Mobile Close Button */}
        <div className="lg:hidden absolute top-4 right-4">
          <button
            onClick={() => setIsMobileMenuOpen(false)}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Admin Panel Header */}
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
              <svg
                className="h-5 w-5 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-lg lg:text-xl text-gray-900 dark:text-gray-100">
                Admin Panel
              </h3>
            </div>
          </div>
        </div>

        {/* Admin Profile */}
        <div className="px-4 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center text-sm font-medium text-white">
                {adminData?.firstname?.charAt(0) || "A"}
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {adminData?.firstname} {adminData?.lastname}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Administrator
                </p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="text-gray-400 hover:text-red-600 dark:text-gray-500 dark:hover:text-red-400 transition-colors p-1"
              title="Logout"
            >
              <svg
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Navigation Menu */}
        <div className="py-2">
          {navItems.map((item) => (
            <button
              key={item.hash}
              onClick={() => handleNavClick(item.hash)}
              className={`w-full flex items-center px-4 py-3 text-sm transition-colors ${
                activeSection === item.hash
                  ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 font-medium border-r-2 border-blue-600 dark:border-blue-400"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              <span className="mr-3">{item.icon}</span>
              <span>{item.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:ml-0">
        {/* Mobile Header */}
        <div className="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100 capitalize">
              {activeSection === "" ? "Dashboard" : activeSection}
            </h1>
            <div className="w-10" /> {/* Spacer for centering */}
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-auto">{renderSectionContent()}</div>
      </div>
    </div>
  );
};

export default AdminContainer;
